
>/Lotus/Upgrades/Mods/Warframe/AvatarAbilityDurationMod
     r|Value=0.05|Value=20
>/Lotus/Upgrades/Mods/Warframe/AvatarAbilityRangeMod
     r|Value=0.075|Value=10
>/Lotus/Upgrades/Mods/Warframe/AvatarAbilityStrengthMod
     r|Value=0.05|Value=20
>/Lotus/Types/Sentinels/SentinelPrecepts/BeastResourceDoublingMod
# 将"富足寻回者"资源加成 Mod 修改为100倍资源获取

# 1. 修改实际游戏效果
Script={
    _baseLuck=100.0
    _luckPerLevel=0
    _doubleCredits=0
    _resourceMultiplier=100.0
}

# 2. 修改UI描述以匹配实际效果
Upgrades={
    {
        UpgradeType=NONE
        OperationType=ADD
        Value=0
        DamageType=DT_ANY
        AutoType=1
        ValidType=""
        SymbolFilter=""
        UpgradeObject=""
        ValidPostures={}
        ValidModifiers={}
        InvalidModifiers={}
        ValidProcTypes={}
        OverrideLocalization=1
        LocTag=/Lotus/Language/Pets/LuckyCompanionResourcesModDesc
        LocKeyWordScript={
            Script=/Lotus/Types/Sentinels/SentinelAbilities/LuckyCompanion.lua
            Function=GetDescriptionInfo
            _baseLuck=100.0
            _luckPerLevel=0
            _doubleResources=100
            _doubleCredits=100
            _resourceMultiplier=100.0
        }
        SmallerIsBetter=0
        RoundTo=0.1
        RoundingMode=RM_ROUND
    }
}
>/Lotus/Types/Sentinels/SentinelPrecepts/BeastUniversalVacuum
  LocalizeTag=吸昏昏
Ability={
    Script={
        Script=/Lotus/Types/Sentinels/SentinelAbilities/WarframeSuckAbility.lua
        Function=NpcEvaluateAbility
        _triggerType=/Lotus/Types/Friendly/Pets/PetVacuumHelper
        _triggerAttachBone=GAME_C1_HIP1
        _triggerRadii={
            200,
            200,
            200,
            200,
            200,
            200
        }
    }
    EnergyRequiredToActivate=0
    ActivationXP=0
    IsPassive=1
}
# 覆盖“Upgrades”代码块中的一部分，以确保UI显示也正确
Upgrades={
    {
        UpgradeType=NONE
        OperationType=ADD
        Value=0
        DamageType=DT_ANY
        AutoType=1
        ValidType=""
        CheckTypeOnInstall=0
        SymbolFilter=""
        UpgradeObject=""
        ValidPostures={}
        ValidModifiers={}
        InvalidModifiers={}
        ValidProcTypes={}
        OverrideLocalization=1
        LocTag=/Lotus/Language/Items/UniversalVacuumDesc
        LocKeyWordScript={
            Script=/Lotus/Types/Sentinels/SentinelAbilities/WarframeSuckAbility.lua
            Function=GetDescriptionInfo
            _triggerType=""
            _triggerAttachBone=GAME_C1_HIP1
            _triggerRadii={
                200,
                200,
                200,
                200,
                200,
                200
            }
        }
        SmallerIsBetter=0
        RoundTo=0.1
        RoundingMode=RM_ROUND
        AllowConditionalLocMerge=0
    }
}
>/Lotus/Types/Friendly/Pets/CatbrowPetPrecepts/CatbrowLuckPrecept
# 招福mod改为每1秒100%概率获得好运
Script={
    _baseLuck=100.0
    _luckPerLevel=0
    _triggerInterval=1.0
    _triggerChance=100.0
}

Upgrades={
    {
        UpgradeType=NONE
        OperationType=ADD
        Value=0
        DamageType=DT_ANY
        AutoType=1
        ValidType=""
        SymbolFilter=""
        UpgradeObject=""
        ValidPostures={}
        ValidModifiers={}
        InvalidModifiers={}
        ValidProcTypes={}
        OverrideLocalization=1
        LocTag=/Lotus/Language/Pets/CatbrowLuckPreceptDesc
        LocKeyWordScript={
            Script=/Lotus/Types/Friendly/Pets/CatbrowPetPrecepts/CatbrowLuckPrecept.lua
            Function=GetDescriptionInfo
            _baseLuck=100.0
            _luckPerLevel=0
            _triggerInterval=1.0
            _triggerChance=100.0
        }
        SmallerIsBetter=0
        RoundTo=0.1
        RoundingMode=RM_ROUND
    }
}
