LocalizeTag = 吸昏昏
Rarity = UNCOMMON
MarketMode = MM_HIDDEN
LocalizeTag = /Lotus/Language / Mods / BeastUniversalVacuum
LocalizeDescTag = ""
Icon = /Lotus/Interface / Cards / Images / Kubrow / BeastUniversalVacuum.png
ExcludeFromCodex = 0
Upgrades = {
{
  UpgradeType = NONE
  OperationType = ADD
  Value = 0
  DamageType = DT_ANY
  AutoType = 1
  ValidType = ""
  CheckTypeOnInstall = 0
  SymbolFilter = ""
  UpgradeObject = ""
  ValidPostures = {}
  ValidModifiers = {}
  InvalidModifiers = {}
  ValidProcTypes = {}
  OverrideLocalization = 1
  LocTag = /Lotus/Language / Items / UniversalVacuumDesc
  LocKeyWordScript = {
    Script=/Lotus/Types / Sentinels / SentinelAbilities / WarframeSuckAbility.lua
Function=GetDescriptionInfo
_triggerType=""
_triggerAttachBone=GAME_C1_HIP1
_triggerRadii={
      8.5,
      9.5,
      10.5,
      11.5,
      12.5,
      13.5
}
  }
  SmallerIsBetter = 0
  RoundTo = 0.1
  RoundingMode = RM_ROUND
  AllowConditionalLocMerge = 0
}
}
ItemCompatibility = /Lotus/Types / Game / Pets / PetPowerSuit
IncludeInBoosterPack = 1
Ability = {
  Script={
    Script=/Lotus/Types / Sentinels / SentinelAbilities / WarframeSuckAbility.lua
Function=NpcEvaluateAbility
_triggerType=/Lotus/Types / Friendly / Pets / PetVacuumHelper
_triggerAttachBone=GAME_C1_HIP1
_triggerRadii={
      8.5,
      9.5,
      10.5,
      11.5,
      12.5,
      13.5
}
  }
EnergyRequiredToActivate=0
ActivationXP=0
IsPassive=1
}
