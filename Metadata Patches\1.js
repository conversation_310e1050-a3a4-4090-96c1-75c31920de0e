// 引入 Node.js 内置的 'fs' (File System) 模块，用于文件操作
const fs = require('fs');

// 定义输入和输出文件的名称
const inputFile = './mod.json';
const outputFile = './res.json';

/**
 * 过滤函数：从原始数据中提取需要的属性
 * @param {Array<Object>} jsonData - 原始的 mod 数据数组
 * @returns {Array<Object>} - 只包含 uniqueName 和 type 的新数组
 */
function filterModData(jsonData) {
  if (!Array.isArray(jsonData)) {
    console.error("错误：输入数据不是一个数组！");
    return [];
  }

  return jsonData.map(mod => {
    // 从 mod 对象中，将 uniqueName 和 type 提取出来（但我们不用它们）
    // ...rest 会收集除了 uniqueName 和 type 之外的所有其他属性，并放入一个名为 rest 的新对象中
    // const { baseDrain, category, compatName, drops, introduced, patchlogs, ...rest } = mod;

    // // 返回这个只包含其余属性的新对象
    // return rest;
    return {
      uniqueName: mod.uniqueName,
      type: mod.type,
      name: mod.name,
      levelStats: mod.levelStats
    };
  });
}
// --- 主程序 ---
try {
  // 1. 读取原始 JSON 文件内容
  // 'utf8' 是文件的编码格式
  console.log(`正在读取文件: ${inputFile}...`);
  const rawData = fs.readFileSync(inputFile, 'utf8');

  // 2. 将读取到的字符串内容解析成 JavaScript 对象（数组）
  const originalJson = JSON.parse(rawData);

  // 3. 调用过滤函数处理数据
  console.log("正在过滤数据...");
  const filteredData = filterModData(originalJson);

  // 4. 将过滤后的 JavaScript 对象转换成格式优美的 JSON 字符串
  // JSON.stringify 的第三个参数 '2' 表示使用 2 个空格进行缩进，让文件更易读
  const outputJsonString = JSON.stringify(filteredData, null, 2);

  // 5. 将字符串写入到新的文件中
  fs.writeFileSync(outputFile, outputJsonString, 'utf8');

  console.log(`处理完成！过滤后的数据已成功保存到 ${outputFile}`);

} catch (error) {
  // 如果过程中出现任何错误（如文件不存在、JSON 格式错误等），在这里捕获并打印
  console.error("处理过程中发生错误:", error.message);
}