$define VK_CONTROL = 0x11
$define VK_SPACE = 0x20
repeat
	local ragdoll = gRegion:GetLocalPlayerAvatar():GetRagdoll()
	if IsNull(ragdoll) then
		local dd = Engine.DamageData()
		dd.injuryType = Injury.RAGDOLL
		gRegion:GetLocalPlayerAvatar():DamageDD(dd)
	elseif get_active_input_filter() ~= "/EE/Types/Input/MenuInputFilter" then
		local rot = gRegion:GetGameCamera():GetRotation()
		if owf_is_key_down('W') then
			ragdoll:ApplyGlobalForce(AngleToDirection(rot), ForceType.FT_VELOCITY_CHANGE)
		end
		if owf_is_key_down('S') then
			ragdoll:ApplyGlobalForce(AngleToDirection(rot) * -1, ForceType.FT_VELOCITY_CHANGE)
		end
		if owf_is_key_down('A') then
			ragdoll:ApplyGlobalForce(AngleToDirection(Rotation(rot.heading - 90, rot.pitch, rot.bank)), ForceType.FT_VELOCITY_CHANGE)
		end
		if owf_is_key_down('D') then
			ragdoll:ApplyGlobalForce(AngleToDirection(Rotation(rot.heading + 90, rot.pitch, rot.bank)), ForceType.FT_VELOCITY_CHANGE)
		end
		if owf_is_key_down(VK_SPACE) then
			ragdoll:ApplyGlobalForce(Vector(0, 1, 0), ForceType.FT_VELOCITY_CHANGE)
		end
		if owf_is_key_down(VK_CONTROL) then
			ragdoll:ApplyGlobalForce(Vector(0, -1, 0), ForceType.FT_VELOCITY_CHANGE)
		end
	end
until not pcall(yield)
gRegion:GetLocalPlayerAvatar():EnableRagdollRecovery(true, false)
